.listing-cta {
    padding: 50px 0 0 0;

    &__background {
        position: relative;
        padding: 40px 0 80px;
        width: 100%;
        
        &::after {
            content: '';
            position: absolute;
            bottom: -2px;
            left: 0;
            width: 100%;
            height: 40px;
            background-image: url('../img/banner-mask.svg');
            background-size: 100% 100%;
            background-repeat: no-repeat;
            background-position: bottom center;
            z-index: 1;
        }
    }

    &__content {
        max-width: 1000px;
        margin: 0 auto;
        display: flex;
        align-items: center;
        gap: 60px;

        @media (max-width: 767px) {
            flex-direction: column;
            text-align: center;
            gap: 20px;
        }
    }

    &__image {
        flex: 0 0 200px;
        
        @media (max-width: 767px) {
            flex: 0 0 auto;
        }
        
        img {
            width: 100%;
            max-width: 200px;
            height: auto;
            display: block;
            border-radius: 50%;
        }
    }

    &__text {
        flex: 1;

        .button {
            margin-right: 30px;

            @media (max-width: 455px) {
                margin: 0 auto;
            }
        }

        .svg-inline--fa {
            transform: scale(1.33);
            margin-right: 10px;
        }
    }

    &__actions {

        display: flex;
        flex-direction: row;
        align-items: center;

        @media (max-width: 455px) {
            flex-direction: column;
            gap: 25px;
        }
    }

    &.cta-top {
        margin-bottom: 50px;
        
        .listing-cta__background {
            background-color: #f5eced;
        }
    }

    &.cta-bottom {
        margin-top: 0;
        margin-bottom: 0;

        .listing-cta__background {
            // Background color and curve removed - now handled by steps-ahead panel
            padding: 60px 0;
        }
    }
} 
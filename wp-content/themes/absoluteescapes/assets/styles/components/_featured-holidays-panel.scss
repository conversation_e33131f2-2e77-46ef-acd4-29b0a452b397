// Featured Holidays Panel Component Styles

.featured-holidays-panel {
    padding: 50px 0;
    background-color: transparent;

    &__inner {
        // AOS fade animation support
    }

    &__container {
        // Full width container
        max-width: none;
        padding: 0;
    }

    &__content {
        // Full width content
        max-width: none;
        margin: 0;
        padding: 0 15px; // Small padding for mobile

        @media (min-width: 768px) {
            padding: 0 15px;
        }
    }

    &__heading {
        text-align: center;
        max-width: 1200px;
        margin: 0 auto 50px auto;
        font-size: 40px;
    }

    &__grid {
        display: grid;
        gap: 10px;
        width: 100%;

        // Dynamic grid based on number of items
        &[data-count="1"] {
            grid-template-columns: 1fr;
            max-width: 400px;
            margin: 0 auto;
        }

        &[data-count="2"] {
            grid-template-columns: repeat(2, 1fr);
        }

        &[data-count="3"] {
            grid-template-columns: repeat(3, 1fr);
        }

        &[data-count="4"] {
            grid-template-columns: repeat(4, 1fr);
        }

        // Responsive adjustments
        @media (max-width: 767px) {
            grid-template-columns: 1fr !important;
            gap: 10px;
        }

        @media (min-width: 768px) and (max-width: 991px) {
            &[data-count="3"],
            &[data-count="4"] {
                grid-template-columns: repeat(2, 1fr);
            }
        }
    }

    &__tile {
        width: 100%;
        padding: 0;
    }

    &__link {
        display: block;
        position: relative;
        text-decoration: none;
        color: inherit;

        &:after {
            content: '';
            display: block;
            position: absolute;
            top: 0;
            right: 0;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0);
            transition: 300ms;
            z-index: 3;
        }

        &:hover, &:focus {
            text-decoration: none;

            &:after {
                background: rgba(0, 0, 0, 0.2);
            }
        }
    }

    &__image {
        position: relative;
        display: flex;
        flex-wrap: wrap;
        align-items: flex-end;
        height: 500px;
        background-size: cover;
        background-position: center;
        background-repeat: no-repeat;

        // Add gradient overlay for text readability
        &::before {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 60%;
            background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
            z-index: 1;
            pointer-events: none;
        }

        @media (max-width: 767px) {
            height: 250px;
        }
    }

    &__overlay {
        position: relative;
        z-index: 2;
        padding: 20px;
        display: flex;
        flex-direction: column;
        justify-content: flex-end;
        align-items: flex-start;
        box-sizing: border-box;
        width: 100%;
    }

    &__content-wrapper {
        width: 100%;
        color: white;
    }

    &__title {
        margin: 0;
        color: white;
    }

    &__price {
        font-weight: 300;
        color: white;

        span {
            color: white;
        }
    }

    &__types {
        position: absolute;
        bottom: 20px;
        right: 20px;
        display: flex;
        gap: 8px;
        margin-bottom: 0;
        flex-wrap: wrap;
    }

    &__type {
        // Individual type styling
    }

    &__icon {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background-color: #3e5056;

        img {
            width: 20px;
            height: 20px;
            filter: brightness(0) invert(1);
        }

        svg {
            width: 22px;
            height: 22px;
        }

        i {
            font-size: 18px;
            color: white;
        }
    }

    &__cta {
        margin-top: auto;
    }

    &__button {
        display: inline-flex;
        align-items: center;
        gap: 8px;
        padding: 8px 16px;
        background-color: rgba(44, 95, 95, 0.9);
        color: white;
        border-radius: 4px;
        font-size: 0.9rem;
        font-weight: 500;
        transition: background-color 0.3s ease;
        border: none;
        text-decoration: none;

        i {
            font-size: 0.8rem;
            transition: transform 0.3s ease;
        }
    }

    // Responsive adjustments
    @media (max-width: 767px) {
        padding: 30px 0;

        &__heading {
            margin-bottom: 30px;
            font-size: 28px;
        }

        &__grid {
            gap: 20px;
            grid-template-columns: 1fr;
        }

        &__overlay {
            padding: 15px;
        }

        &__types {
            bottom: 15px;
            right: 15px;
        }

        &__icon {
            width: 32px;
            height: 32px;

            img {
                width: 16px;
                height: 16px;
            }

            svg {
                width: 18px;
                height: 18px;
            }

            i {
                font-size: 14px;
            }
        }
    }
}

// Section heading for "All holidays"
.holidays-results__section-heading {
    margin-bottom: 30px;
    text-align: center;

    .holidays-results__heading {
        margin-bottom: 0;
        font-size: 40px;

        @media (max-width: 767px) {
            font-size: 28px;
        }
    }
}



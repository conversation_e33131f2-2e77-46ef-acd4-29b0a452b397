<?php 

/**
 * Steps Ahead
 */

$heading = get_sub_field('heading');
$copy = get_sub_field('copy');
$background_color = get_sub_field('background_color');
$curved_edge = get_sub_field('curved_edge');

// Set up background classes
$background_classes = '';
if ($background_color) {
    $background_classes .= ' has-background';
}
if ($curved_edge && $curved_edge !== 'none') {
    $background_classes .= ' curve-' . $curved_edge;
}

?>

<section class="steps-ahead">
    <div class="steps-ahead__inner<?php echo $background_classes; ?>" data-aos="fade"<?php if ($background_color) : ?> style="background-color: <?php echo $background_color; ?>;"<?php endif; ?>>
        <div class="container steps-ahead__container">
            <div class="steps-ahead__content centre inner-container">
                <?php if($heading) : ?>
                    <h2 class="steps-ahead__heading h2-large heading-light heading-underlined text-weight-regular"><?php echo $heading; ?></h2>
                <?php endif; ?>
                <?php if($copy) : ?>
                    <div class="content-area steps-ahead__copy">
                        <?php echo $copy; ?>
                    </div>
                <?php endif; ?>
            </div>
            <?php if(have_rows('columns')) : ?>
                <div class="row steps-ahead__row">
                    <?php while(have_rows('columns')) : the_row(); ?>
                        <?php

                        $image = get_sub_field('image');
                        $cheading = get_sub_field('heading');
                        $ccopy = get_sub_field('copy');

                        ?>

                        <div class="col-md-3 steps-ahead__col">
                            <div class="steps-ahead__col-content centre">
                                <?php if($image) : ?>
                                    <div class="steps-ahead__image">
                                        <img src="<?php echo $image['url']; ?>" alt="<?php echo $image['alt']; ?>">
                                    </div>
                                <?php endif; ?>
                                <?php if($cheading) : ?>
                                    <h4 class="steps-ahead__heading"><?php echo $cheading; ?></h4>
                                <?php endif; ?>
                                <?php if($ccopy) : ?>
                                    <div class="steps-ahead__copy content-area">
                                        <?php echo $ccopy; ?>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>

                    <?php endwhile; ?>
                </div>
            <?php endif; ?>
        </div>
    </div>
</section><!-- .steps-ahead -->